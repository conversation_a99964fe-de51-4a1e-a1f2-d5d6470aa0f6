/* ========================================
   筛选图标垂直居中样式 - 不修改源码方案
   ======================================== */

/* CSS变量定义 */
:root {
  --filter-icon-size: 16px;
  --filter-icon-padding: 2px;
  --filter-icon-offset: 8px; /* 居中偏移量 */
}

/* 全局容器样式 */
.univer-render-canvas,
.univer-canvas-container {
  position: relative;
}

/* 核心：通过CSS选择器匹配筛选相关元素并居中 */
/* 匹配可能的筛选按钮类名 */
[class*="filter-button"],
[class*="FilterButton"],
[class*="filter_button"],
[class*="sheets-filter"],
[class*="SheetsFilter"],
[class*="table-filter"],
[class*="TableFilter"] {
  /* 强制垂直居中 */
  transform: translateY(-50%) !important;
  top: 50% !important;
  position: relative !important;
}

/* 针对Canvas内的筛选图标进行位置调整 */
canvas + * [class*="filter"],
canvas ~ * [class*="filter"] {
  transform: translateY(-50%) !important;
  top: 50% !important;
}

/* 通过属性选择器匹配筛选相关元素 */
[data-filter],
[data-filter-button],
[aria-label*="filter" i],
[aria-label*="筛选" i],
[title*="filter" i],
[title*="筛选" i] {
  transform: translateY(-50%) !important;
  top: 50% !important;
  position: relative !important;
}

/* SVG图标居中 */
svg[class*="filter"],
svg[data-icon*="filter"] {
  vertical-align: middle;
  transform: translateY(-2px);
}

/* 图标容器居中 */
.univer-sheet-container [class*="icon"],
.univer-canvas-container [class*="icon"] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 自定义筛选图标样式 */
.filter-icon-centered {
  width: var(--filter-icon-size);
  height: var(--filter-icon-size);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

.filter-icon-centered:hover {
  opacity: 0.7;
  transform: translateY(-50%) scale(1.1);
}

.filter-icon-centered.has-criteria {
  color: #1976d2;
  fill: #1976d2;
}

/* 通过伪元素强制居中 */
.univer-canvas-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

/* 响应式调整 */
@media (max-width: 768px) {
  :root {
    --filter-icon-size: 14px;
    --filter-icon-padding: 1px;
    --filter-icon-offset: 7px;
  }
}

/* 深色主题适配 */
.univer-dark [class*="filter"] {
  color: #ccc;
}

.univer-dark [class*="filter"]:hover {
  color: #fff;
}

.univer-dark [class*="filter"].has-criteria {
  color: #64b5f6;
}
