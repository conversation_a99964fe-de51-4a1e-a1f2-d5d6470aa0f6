/* 自定义筛选图标样式 */

/* 筛选图标容器的基础样式调整 */
.univer-render-canvas {
  position: relative;
}

/* 如果有特定的筛选图标类名，进行位置调整 */
.filter-button,
.sheets-filter-button,
[class*="filter-button"] {
  /* 确保图标垂直居中 */
  transform: translateY(-50%) !important;
  top: 50% !important;
}

/* 针对Canvas渲染的筛选图标进行样式调整 */
.univer-canvas-container canvas {
  /* 确保canvas正确处理筛选图标的位置 */
}

/* 自定义筛选图标的悬停效果 */
.filter-icon:hover,
.sheets-filter-icon:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
}

/* 筛选图标的基础定位调整 */
.univer-sheet-container {
  --filter-icon-size: 16px;
  --filter-icon-padding: 2px;
  --filter-icon-vertical-align: center;
}

/* 如果存在SVG筛选图标，进行样式调整 */
svg.filter-icon,
svg[class*="filter"] {
  vertical-align: middle;
  transform: translateY(-1px); /* 微调垂直位置 */
}

/* 针对表格筛选按钮的特殊样式 */
.univer-table-filter-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 自定义筛选图标的颜色和大小 */
.custom-filter-icon {
  width: 16px;
  height: 16px;
  fill: #666;
  stroke: #666;
  stroke-width: 1.5;
}

.custom-filter-icon:hover {
  fill: #333;
  stroke: #333;
}

/* 有筛选条件时的图标样式 */
.custom-filter-icon.has-criteria {
  fill: #1976d2;
  stroke: #1976d2;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .custom-filter-icon {
    width: 14px;
    height: 14px;
  }
}

/* 深色主题适配 */
.univer-dark .custom-filter-icon {
  fill: #ccc;
  stroke: #ccc;
}

.univer-dark .custom-filter-icon:hover {
  fill: #fff;
  stroke: #fff;
}

.univer-dark .custom-filter-icon.has-criteria {
  fill: #64b5f6;
  stroke: #64b5f6;
}
