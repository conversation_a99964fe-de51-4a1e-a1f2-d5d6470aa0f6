/**
 * 筛选图标自定义器 - 适用于Preset模式
 * 通过DOM操作和样式注入来实现筛选图标的居中和自定义
 */

class FilterIconCustomizer {
  constructor(options = {}) {
    this.options = {
      iconSize: 16,
      padding: 2,
      verticalAlign: 'center', // 'top', 'center', 'bottom'
      customIcon: null, // 自定义图标SVG路径
      ...options
    };
    
    this.init();
  }

  init() {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }
  }

  setup() {
    this.injectStyles();
    this.observeCanvasChanges();
    this.patchFilterRendering();
    console.log('✅ Filter Icon Customizer initialized');
  }

  /**
   * 注入自定义样式
   */
  injectStyles() {
    const styleId = 'filter-icon-customizer-styles';
    
    if (document.getElementById(styleId)) {
      return; // 避免重复注入
    }

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      /* 筛选图标自定义样式 */
      .univer-filter-icon-container {
        position: relative;
        display: inline-flex;
        align-items: ${this.getAlignItems()};
        justify-content: flex-end;
        width: 100%;
        height: 100%;
        pointer-events: none;
      }
      
      .univer-filter-icon {
        width: ${this.options.iconSize}px;
        height: ${this.options.iconSize}px;
        margin-right: ${this.options.padding}px;
        pointer-events: auto;
        cursor: pointer;
        transition: all 0.2s ease;
      }
      
      .univer-filter-icon:hover {
        opacity: 0.7;
        transform: scale(1.1);
      }
      
      .univer-filter-icon.has-criteria {
        color: #1976d2;
        fill: #1976d2;
      }
      
      /* Canvas相关的样式调整 */
      .univer-render-canvas {
        position: relative;
      }
      
      /* 确保筛选图标在正确的层级 */
      .filter-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1000;
      }
    `;
    
    document.head.appendChild(style);
  }

  /**
   * 获取对齐方式对应的CSS值
   */
  getAlignItems() {
    switch (this.options.verticalAlign) {
      case 'top': return 'flex-start';
      case 'bottom': return 'flex-end';
      case 'center':
      default: return 'center';
    }
  }

  /**
   * 观察Canvas变化
   */
  observeCanvasChanges() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node;
              if (element.tagName === 'CANVAS' || 
                  element.classList?.contains('univer-render-canvas')) {
                this.processCanvas(element);
              }
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // 处理已存在的canvas
    setTimeout(() => {
      const canvases = document.querySelectorAll('canvas, .univer-render-canvas');
      canvases.forEach(canvas => this.processCanvas(canvas));
    }, 500);
  }

  /**
   * 处理Canvas元素
   */
  processCanvas(canvas) {
    if (canvas.dataset.filterProcessed) {
      return; // 避免重复处理
    }
    
    canvas.dataset.filterProcessed = 'true';
    
    // 为canvas添加筛选图标覆盖层
    this.addFilterOverlay(canvas);
    
    console.log('Canvas processed for filter icon customization:', canvas);
  }

  /**
   * 添加筛选图标覆盖层
   */
  addFilterOverlay(canvas) {
    const container = canvas.parentElement;
    if (!container) return;

    // 创建覆盖层
    const overlay = document.createElement('div');
    overlay.className = 'filter-overlay';
    overlay.style.position = 'absolute';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.pointerEvents = 'none';
    overlay.style.zIndex = '1000';

    container.style.position = 'relative';
    container.appendChild(overlay);

    // 监听canvas的重绘事件
    this.monitorCanvasRedraw(canvas, overlay);
  }

  /**
   * 监听Canvas重绘
   */
  monitorCanvasRedraw(canvas, overlay) {
    // 使用ResizeObserver监听canvas大小变化
    if (window.ResizeObserver) {
      const resizeObserver = new ResizeObserver(() => {
        this.updateFilterIcons(canvas, overlay);
      });
      resizeObserver.observe(canvas);
    }

    // 定期检查并更新筛选图标
    setInterval(() => {
      this.updateFilterIcons(canvas, overlay);
    }, 1000);
  }

  /**
   * 更新筛选图标
   */
  updateFilterIcons(canvas, overlay) {
    // 这里可以根据实际需要来检测和更新筛选图标的位置
    // 由于Univer使用Canvas渲染，我们需要通过其他方式来检测筛选区域
    
    // 示例：检查是否有筛选相关的数据属性或事件
    if (canvas.dataset.hasFilter) {
      this.renderCustomFilterIcons(overlay);
    }
  }

  /**
   * 渲染自定义筛选图标
   */
  renderCustomFilterIcons(overlay) {
    // 清除现有图标
    overlay.innerHTML = '';

    // 这里需要根据实际的筛选数据来渲染图标
    // 示例实现
    const filterIcon = this.createFilterIcon();
    overlay.appendChild(filterIcon);
  }

  /**
   * 创建筛选图标元素
   */
  createFilterIcon(hasCriteria = false) {
    const icon = document.createElement('div');
    icon.className = `univer-filter-icon ${hasCriteria ? 'has-criteria' : ''}`;
    
    if (this.options.customIcon) {
      icon.innerHTML = this.options.customIcon;
    } else {
      // 默认筛选图标SVG
      icon.innerHTML = `
        <svg width="${this.options.iconSize}" height="${this.options.iconSize}" viewBox="0 0 16 16" fill="none">
          <path d="M3 4H13M4.5 8H11.5M6 12H10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
        </svg>
      `;
    }

    // 添加点击事件
    icon.addEventListener('click', (e) => {
      e.stopPropagation();
      this.handleFilterClick(icon);
    });

    return icon;
  }

  /**
   * 处理筛选图标点击
   */
  handleFilterClick(icon) {
    console.log('Filter icon clicked:', icon);
    // 这里可以触发筛选面板的显示
    // 需要根据实际的Univer API来实现
  }

  /**
   * 修补筛选渲染逻辑
   */
  patchFilterRendering() {
    // 尝试修改全局的筛选相关函数
    if (window.univerAPI) {
      this.patchUniverAPI();
    }

    // 监听Univer实例的创建
    const originalCreateUniver = window.createUniver;
    if (originalCreateUniver) {
      window.createUniver = (...args) => {
        const result = originalCreateUniver.apply(this, args);
        this.enhanceUniverInstance(result);
        return result;
      };
    }
  }

  /**
   * 修补Univer API
   */
  patchUniverAPI() {
    // 这里可以添加对Univer API的修改
    console.log('Patching Univer API for filter customization');
  }

  /**
   * 增强Univer实例
   */
  enhanceUniverInstance(univerInstance) {
    if (univerInstance && univerInstance.univerAPI) {
      // 添加自定义方法到API
      univerInstance.univerAPI.customizeFilterIcon = (options) => {
        Object.assign(this.options, options);
        this.injectStyles(); // 重新注入样式
      };
    }
  }
}

// 自动初始化
const filterCustomizer = new FilterIconCustomizer({
  iconSize: 16,
  padding: 2,
  verticalAlign: 'center'
});

// 导出供外部使用
window.FilterIconCustomizer = FilterIconCustomizer;
window.filterCustomizer = filterCustomizer;
