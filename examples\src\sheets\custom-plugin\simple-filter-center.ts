/**
 * Copyright 2023-present DreamNum Co., Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { Inject, Injector, Plugin } from '@univerjs/core';
import { IRenderManagerService } from '@univerjs/engine-render';

/**
 * 简单的筛选图标居中插件
 * 通过修改现有筛选按钮的位置计算来实现居中效果
 */
export class SimpleFilterCenterPlugin extends Plugin {
    static override pluginName = 'simple-filter-center-plugin';

    constructor(
        _config: null,
        @Inject(Injector) readonly _injector: Injector,
        @Inject(IRenderManagerService) private readonly _renderManagerService: IRenderManagerService
    ) {
        super();
    }

    override onStarting(): void {
        this._patchFilterButtonPosition();
    }

    /**
     * 修补筛选按钮位置计算逻辑
     */
    private _patchFilterButtonPosition(): void {
        // 方法1: 通过修改全局样式来调整筛选图标位置
        this._addCustomStyles();
        
        // 方法2: 通过拦截渲染过程来修改位置
        this._interceptFilterRendering();
    }

    /**
     * 添加自定义样式来调整筛选图标位置
     */
    private _addCustomStyles(): void {
        const style = document.createElement('style');
        style.textContent = `
            /* 自定义筛选图标样式 */
            .univer-filter-button {
                /* 如果有对应的CSS类名，可以通过这种方式调整 */
                transform: translateY(-50%) !important;
                top: 50% !important;
            }
            
            /* 或者通过canvas相关的样式调整 */
            .univer-canvas-container .filter-icon {
                vertical-align: middle;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 拦截筛选渲染过程
     */
    private _interceptFilterRendering(): void {
        // 由于Univer使用Canvas渲染，我们需要在渲染层面进行拦截
        // 这里提供一个概念性的实现
        
        // 获取渲染管理器
        const renderManagerService = this._renderManagerService;
        
        // 监听渲染事件
        console.log('Filter center plugin: Intercepting filter rendering...');
        
        // 实际实现需要深入到渲染层面
        this._overrideFilterButtonCreation();
    }

    /**
     * 覆盖筛选按钮创建逻辑
     */
    private _overrideFilterButtonCreation(): void {
        // 这里是一个示例，展示如何通过修改原型来改变行为
        
        // 保存原始方法的引用
        const originalMethod = this._getOriginalFilterMethod();
        
        if (originalMethod) {
            // 创建新的方法来替换原始方法
            const centeredMethod = this._createCenteredFilterMethod(originalMethod);
            
            // 替换原始方法
            this._replaceFilterMethod(centeredMethod);
        }
    }

    /**
     * 获取原始筛选方法
     */
    private _getOriginalFilterMethod(): Function | null {
        // 这里需要根据实际的Univer内部结构来获取
        // 由于我们不能直接修改源码，这里提供一个概念性的实现
        
        try {
            // 尝试获取筛选相关的渲染控制器
            const filterController = this._injector.get('SheetsFilterRenderController' as any);
            return filterController?._renderFilterButtons?.bind(filterController);
        } catch (error) {
            console.warn('Could not find filter render controller:', error);
            return null;
        }
    }

    /**
     * 创建居中的筛选方法
     */
    private _createCenteredFilterMethod(originalMethod: Function): Function {
        return function(this: any, ...args: any[]) {
            // 调用原始方法
            const result = originalMethod.apply(this, args);
            
            // 修改筛选按钮的位置
            if (this._filterButtonShapes) {
                this._filterButtonShapes.forEach((shape: any) => {
                    if (shape && shape.setShapeProps) {
                        const currentProps = shape.getShapeProps();
                        if (currentProps) {
                            // 重新计算垂直居中位置
                            const cellHeight = currentProps.cellHeight || 20;
                            const iconSize = 16;
                            const newTop = currentProps.top - (cellHeight - iconSize) / 2;
                            
                            shape.setShapeProps({
                                ...currentProps,
                                top: newTop
                            });
                        }
                    }
                });
            }
            
            return result;
        };
    }

    /**
     * 替换筛选方法
     */
    private _replaceFilterMethod(newMethod: Function): void {
        try {
            const filterController = this._injector.get('SheetsFilterRenderController' as any);
            if (filterController && filterController._renderFilterButtons) {
                filterController._renderFilterButtons = newMethod;
                console.log('Filter button rendering method replaced successfully');
            }
        } catch (error) {
            console.warn('Could not replace filter method:', error);
        }
    }
}

/**
 * 更直接的解决方案：通过配置来修改筛选图标位置
 */
export class FilterIconConfigPlugin extends Plugin {
    static override pluginName = 'filter-icon-config-plugin';

    constructor(
        _config: null,
        @Inject(Injector) readonly _injector: Injector
    ) {
        super();
    }

    override onStarting(): void {
        // 通过修改全局配置来影响筛选图标的渲染
        this._modifyFilterConfig();
    }

    private _modifyFilterConfig(): void {
        // 如果Univer提供了筛选相关的配置选项，可以在这里修改
        console.log('Modifying filter icon configuration for centering...');
        
        // 示例：修改筛选图标的默认位置计算
        const FILTER_ICON_SIZE = 16;
        const FILTER_ICON_PADDING = 1;
        
        // 创建一个全局函数来计算居中位置
        (window as any).calculateCenteredFilterPosition = (
            startX: number,
            startY: number,
            endX: number,
            endY: number
        ) => {
            const cellWidth = endX - startX;
            const cellHeight = endY - startY;
            
            return {
                iconStartX: endX - FILTER_ICON_SIZE - FILTER_ICON_PADDING,
                iconStartY: startY + (cellHeight - FILTER_ICON_SIZE) / 2, // 垂直居中
                cellWidth,
                cellHeight
            };
        };
    }
}

export default SimpleFilterCenterPlugin;
