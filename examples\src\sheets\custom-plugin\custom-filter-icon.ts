/**
 * Copyright 2023-present DreamNum Co., Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { IMouseEvent, IPointerEvent, IShapeProps, UniverRenderingContext2D } from '@univerjs/engine-render';
import type { IRange } from '@univerjs/core';
import { ICommandService, IContextService, Inject, Injector, Plugin, ThemeService } from '@univerjs/core';
import { Shape } from '@univerjs/engine-render';
import { IRenderManagerService } from '@univerjs/engine-render';
import { getCoordByCell, SheetSkeletonManagerService } from '@univerjs/sheets-ui';

// 自定义筛选图标配置
export const CUSTOM_FILTER_ICON_SIZE = 16;
export const CUSTOM_FILTER_ICON_PADDING = 2;

// 自定义筛选图标形状接口
export interface ICustomFilterButtonShapeProps extends IShapeProps {
    cellWidth: number;
    cellHeight: number;
    filterParams: { unitId: string; subUnitId: string; col: number; hasCriteria: boolean };
}

/**
 * 自定义筛选按钮形状类
 * 实现上下居中的筛选图标
 */
export class CustomFilterButtonShape extends Shape<ICustomFilterButtonShapeProps> {
    private _cellWidth!: number;
    private _cellHeight!: number;
    private _filterParams!: ICustomFilterButtonShapeProps['filterParams'];
    private _hovered = false;

    constructor(
        key: string,
        props: ICustomFilterButtonShapeProps,
        @IContextService private readonly _contextService: IContextService,
        @ICommandService private readonly _commandService: ICommandService,
        @Inject(ThemeService) private readonly _themeService: ThemeService
    ) {
        super(key, props);
        this.setShapeProps(props);

        // 绑定事件
        this.onPointerDown$.subscribeEvent((evt) => this.onPointerDown(evt));
        this.onPointerEnter$.subscribeEvent(() => this.onPointerEnter());
        this.onPointerLeave$.subscribeEvent(() => this.onPointerLeave());
    }

    setShapeProps(props: ICustomFilterButtonShapeProps): void {
        const { cellWidth, cellHeight, filterParams } = props;
        this._cellWidth = cellWidth;
        this._cellHeight = cellHeight;
        this._filterParams = filterParams;
    }

    private onPointerDown(evt: IPointerEvent | IMouseEvent): void {
        // 处理点击事件，打开筛选面板
        const { col, unitId, subUnitId } = this._filterParams;

        // 这里可以触发打开筛选面板的命令
        // this._commandService.executeCommand('sheets-filter.operation.open-filter-panel', {
        //     unitId,
        //     subUnitId,
        //     col
        // });

        evt.stopPropagation();
    }

    private onPointerEnter(): void {
        this._hovered = true;
        this.makeDirty(true);
    }

    private onPointerLeave(): void {
        this._hovered = false;
        this.makeDirty(true);
    }

    protected override _draw(ctx: UniverRenderingContext2D): void {
        const cellHeight = this._cellHeight;
        const cellWidth = this._cellWidth;

        // 计算裁剪区域
        const left = CUSTOM_FILTER_ICON_SIZE - cellWidth;
        const top = CUSTOM_FILTER_ICON_SIZE - cellHeight;

        ctx.save();

        // 设置裁剪区域
        const cellRegion = new Path2D();
        cellRegion.rect(left, top, cellWidth, cellHeight);
        ctx.clip(cellRegion);

        const { hasCriteria } = this._filterParams;

        // 获取主题颜色
        const fgColor = this._themeService.getColorFromTheme('primary.600');
        const bgColor = this._hovered
            ? this._themeService.getColorFromTheme('gray.50')
            : 'rgba(255, 255, 255, 1.0)';

        // 绘制自定义筛选图标
        if (hasCriteria) {
            this.drawCustomFilterIconWithCriteria(ctx, CUSTOM_FILTER_ICON_SIZE, fgColor, bgColor);
        } else {
            this.drawCustomFilterIcon(ctx, CUSTOM_FILTER_ICON_SIZE, fgColor, bgColor);
        }

        ctx.restore();
    }

    /**
     * 绘制自定义筛选图标（无筛选条件）
     */
    private drawCustomFilterIcon(ctx: UniverRenderingContext2D, size: number, fgColor: string, bgColor: string): void {
        ctx.save();

        // 绘制背景
        ctx.fillStyle = bgColor;
        ctx.fillRect(0, 0, size, size);

        // 绘制边框
        ctx.strokeStyle = this._hovered ? fgColor : 'rgba(0, 0, 0, 0.1)';
        ctx.lineWidth = 1;
        ctx.strokeRect(0.5, 0.5, size - 1, size - 1);

        // 绘制自定义筛选图标 - 漏斗形状
        ctx.strokeStyle = fgColor;
        ctx.lineWidth = 1.5;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';

        // 漏斗顶部
        ctx.beginPath();
        ctx.moveTo(3, 4);
        ctx.lineTo(13, 4);

        // 漏斗中部
        ctx.moveTo(5, 7);
        ctx.lineTo(11, 7);

        // 漏斗底部
        ctx.moveTo(7, 10);
        ctx.lineTo(9, 10);
        ctx.lineTo(9, 13);

        ctx.stroke();

        ctx.restore();
    }

    /**
     * 绘制自定义筛选图标（有筛选条件）
     */
    private drawCustomFilterIconWithCriteria(ctx: UniverRenderingContext2D, size: number, fgColor: string, bgColor: string): void {
        ctx.save();

        // 绘制背景
        ctx.fillStyle = bgColor;
        ctx.fillRect(0, 0, size, size);

        // 绘制边框
        ctx.strokeStyle = fgColor;
        ctx.lineWidth = 1;
        ctx.strokeRect(0.5, 0.5, size - 1, size - 1);

        // 绘制填充的漏斗图标表示有筛选条件
        ctx.fillStyle = fgColor;

        // 绘制漏斗形状
        ctx.beginPath();
        ctx.moveTo(3, 4);
        ctx.lineTo(13, 4);
        ctx.lineTo(11, 7);
        ctx.lineTo(5, 7);
        ctx.closePath();
        ctx.fill();

        // 绘制漏斗下半部分
        ctx.beginPath();
        ctx.moveTo(7, 10);
        ctx.lineTo(9, 10);
        ctx.lineTo(9, 13);
        ctx.lineTo(7, 13);
        ctx.closePath();
        ctx.fill();

        ctx.restore();
    }
}

/**
 * 自定义筛选渲染控制器
 * 替换默认的筛选按钮渲染逻辑，实现居中效果
 */
export class CustomFilterRenderController {
    private _filterButtonShapes: CustomFilterButtonShape[] = [];

    constructor(
        private readonly _injector: Injector,
        private readonly _scene: any,
        private readonly _skeleton: any
    ) {}

    /**
     * 渲染居中的筛选按钮
     */
    renderCenteredFilterButtons(
        unitId: string,
        worksheetId: string,
        range: IRange,
        filterModel: any
    ): void {
        // 清除现有的筛选按钮
        this.clearFilterButtons();

        const { startColumn, endColumn, startRow } = range;

        for (let col = startColumn; col <= endColumn; col++) {
            const key = `custom-filter-button-${col}`;
            const startPosition = getCoordByCell(startRow, col, this._scene, this._skeleton);
            const { startX, startY, endX, endY } = startPosition;

            const cellWidth = endX - startX;
            const cellHeight = endY - startY;

            // 检查是否有足够空间绘制按钮
            if (cellHeight <= CUSTOM_FILTER_ICON_PADDING || cellWidth <= CUSTOM_FILTER_ICON_PADDING) {
                continue;
            }

            const hasCriteria = !!filterModel?.getFilterColumn?.(col);

            // 使用静态方法创建居中的筛选按钮
            const buttonShape = CustomFilterIconPlugin.createCenteredFilterButton(
                this._injector,
                key,
                startX,
                startY,
                endX,
                endY,
                { unitId, subUnitId: worksheetId, col, hasCriteria }
            );

            this._filterButtonShapes.push(buttonShape);
        }

        // 添加到场景中
        this._scene.addObjects(this._filterButtonShapes);
        this._scene.makeDirty();
    }

    /**
     * 清除筛选按钮
     */
    clearFilterButtons(): void {
        this._filterButtonShapes.forEach(shape => {
            this._scene.removeObject(shape);
        });
        this._filterButtonShapes = [];
    }
}

/**
 * 自定义筛选图标插件
 */
export class CustomFilterIconPlugin extends Plugin {
    static override pluginName = 'custom-filter-icon-plugin';

    constructor(
        _config: null,
        @Inject(Injector) readonly _injector: Injector,
        @Inject(IRenderManagerService) private readonly _renderManagerService: IRenderManagerService
    ) {
        super();
    }

    override onStarting(): void {
        // 注册自定义渲染控制器
        this._registerCustomFilterRenderer();
    }

    private _registerCustomFilterRenderer(): void {
        // 这里可以注册自定义的筛选渲染控制器
        // 替换默认的筛选按钮渲染逻辑
        console.log('Custom filter icon plugin started');

        // 可以通过依赖注入系统替换默认的筛选渲染器
        // 或者监听筛选相关事件来自定义渲染
    }

    /**
     * 创建居中的筛选按钮
     */
    static createCenteredFilterButton(
        injector: Injector,
        key: string,
        startX: number,
        startY: number,
        endX: number,
        endY: number,
        filterParams: { unitId: string; subUnitId: string; col: number; hasCriteria: boolean }
    ): CustomFilterButtonShape {
        const cellWidth = endX - startX;
        const cellHeight = endY - startY;

        // 计算居中位置
        const iconStartX = endX - CUSTOM_FILTER_ICON_SIZE - CUSTOM_FILTER_ICON_PADDING;
        // 关键：垂直居中计算
        const iconStartY = startY + (cellHeight - CUSTOM_FILTER_ICON_SIZE) / 2;

        const props: ICustomFilterButtonShapeProps = {
            left: iconStartX,
            top: iconStartY,
            height: CUSTOM_FILTER_ICON_SIZE,
            width: CUSTOM_FILTER_ICON_SIZE,
            zIndex: 5000,
            cellHeight,
            cellWidth,
            filterParams,
        };

        return injector.createInstance(CustomFilterButtonShape, key, props);
    }
}

export default CustomFilterIconPlugin;
