# 筛选图标垂直居中 - 不修改源码方案

## 🎯 方案概述

本方案提供了三种不修改Univer源码的方式来实现筛选图标的垂直居中：

1. **纯CSS方案** - 通过CSS选择器和样式覆盖
2. **JavaScript动态调整** - 通过DOM操作和样式注入
3. **组合方案** - CSS + JavaScript 的完整解决方案

## 🚀 快速开始

### 方案一：纯CSS方案（最简单）

只需要在您的CSS文件中添加以下样式：

```css
/* 筛选图标垂直居中的关键样式 */
[class*="filter-button"],
[class*="FilterButton"], 
[class*="filter_button"],
[class*="sheets-filter"],
[class*="SheetsFilter"],
[class*="table-filter"],
[class*="TableFilter"] {
  transform: translateY(-50%) !important;
  top: 50% !important;
  position: relative !important;
}

/* 通过属性选择器匹配筛选相关元素 */
[data-filter],
[data-filter-button],
[aria-label*="filter" i],
[aria-label*="筛选" i] {
  transform: translateY(-50%) !important;
  top: 50% !important;
  position: relative !important;
}
```

### 方案二：JavaScript动态方案（最智能）

1. **引入脚本文件**：
```html
<script src="./filter-center-external.js"></script>
```

2. **配置选项**：
```javascript
// 配置筛选图标居中
window.configureFilterCenter({
    iconSize: 16,
    padding: 2,
    verticalAlign: 'center', // 'top', 'center', 'bottom'
    debug: true
});
```

### 方案三：组合方案（最完整）

使用提供的 `index.html` 文件，它集成了CSS和JavaScript两种方案。

## 📁 文件说明

### 核心文件

1. **`style.css`** - 包含筛选图标居中的CSS样式
2. **`filter-center-external.js`** - 外部JavaScript脚本，动态处理筛选图标
3. **`index.html`** - 完整的HTML页面示例

### 使用方式

#### 在您现有的项目中集成：

1. **复制CSS样式**：
   将 `style.css` 中的筛选相关样式复制到您的样式文件中

2. **引入JavaScript脚本**：
   ```html
   <script src="./filter-center-external.js"></script>
   ```

3. **配置和初始化**：
   ```javascript
   document.addEventListener('DOMContentLoaded', function() {
       // 配置筛选图标
       window.configureFilterCenter({
           iconSize: 16,
           padding: 2,
           verticalAlign: 'center'
       });
       
       // 延迟处理，确保Univer完全加载
       setTimeout(function() {
           window.processFilterElements();
       }, 3000);
   });
   ```

## ⚙️ 配置选项

### CSS变量配置

```css
:root {
  --filter-icon-size: 16px;        /* 图标大小 */
  --filter-icon-padding: 2px;      /* 边距 */
  --filter-icon-offset: 8px;       /* 居中偏移量 */
}
```

### JavaScript配置

```javascript
window.configureFilterCenter({
    iconSize: 16,                   // 图标大小
    padding: 2,                     // 边距
    verticalAlign: 'center',        // 垂直对齐：'top', 'center', 'bottom'
    checkInterval: 1000,            // 检查间隔（毫秒）
    debug: true                     // 是否显示调试信息
});
```

## 🎨 自定义样式

### 不同对齐方式

```javascript
// 顶部对齐
window.setFilterAlignment('top');

// 居中对齐（推荐）
window.setFilterAlignment('center');

// 底部对齐
window.setFilterAlignment('bottom');
```

### 调整图标大小

```javascript
// 设置图标大小为18px
window.setFilterIconSize(18);

// 设置图标大小为14px（适合移动端）
window.setFilterIconSize(14);
```

### 自定义颜色

```css
/* 正常状态 */
[class*="filter"] {
    color: #666 !important;
}

/* 悬停状态 */
[class*="filter"]:hover {
    color: #333 !important;
}

/* 有筛选条件时 */
[class*="filter"].has-criteria {
    color: #1976d2 !important;
}
```

## 🔍 调试和验证

### 检查是否生效

1. **查看控制台输出**：
```
[FilterCenter] Initializing external filter center...
[FilterCenter] Styles injected
[FilterCenter] DOM observer started
[FilterCenter] Processed 3 filter elements
```

2. **手动触发处理**：
```javascript
// 手动处理筛选元素
window.processFilterElements();
```

3. **检查元素类名**：
筛选元素应该包含 `filter-center-processed` 类名

### 常见问题排查

1. **样式不生效**：
   - 检查CSS是否正确加载
   - 确认选择器优先级足够高（使用 `!important`）

2. **JavaScript不工作**：
   - 检查脚本是否正确加载
   - 确认没有JavaScript错误

3. **筛选图标找不到**：
   - 检查元素的实际类名
   - 调整选择器匹配规则

## 📱 响应式支持

```css
/* 移动端适配 */
@media (max-width: 768px) {
  :root {
    --filter-icon-size: 14px;
    --filter-icon-padding: 1px;
  }
}

/* 平板适配 */
@media (max-width: 1024px) {
  :root {
    --filter-icon-size: 15px;
    --filter-icon-padding: 1.5px;
  }
}
```

## 🌙 深色主题支持

```css
/* 深色主题适配 */
.univer-dark [class*="filter"] {
    color: #ccc !important;
}

.univer-dark [class*="filter"]:hover {
    color: #fff !important;
}

.univer-dark [class*="filter"].has-criteria {
    color: #64b5f6 !important;
}
```

## ✅ 兼容性

- ✅ 支持所有现代浏览器
- ✅ 兼容Univer Preset模式
- ✅ 不影响现有功能
- ✅ 支持动态加载的内容
- ✅ 支持深色主题
- ✅ 响应式设计

## 🔄 更新和维护

### 如果Univer更新后失效：

1. **检查新的类名**：
   查看筛选元素是否使用了新的类名

2. **更新选择器**：
   在CSS和JavaScript中添加新的选择器

3. **调整样式**：
   根据新的DOM结构调整样式

### 自定义扩展：

```javascript
// 扩展选择器
const customSelectors = [
    '.my-custom-filter',
    '[data-my-filter]'
];

// 添加到现有配置
window.configureFilterCenter({
    customSelectors: customSelectors
});
```

## 📞 技术支持

如果遇到问题：

1. 检查浏览器控制台的错误信息
2. 确认所有文件都正确加载
3. 验证CSS选择器是否匹配到目标元素
4. 检查JavaScript配置是否正确

## 🎉 总结

这个方案的优势：

- ✅ **不修改源码** - 完全外部实现
- ✅ **易于集成** - 只需添加CSS和JS文件
- ✅ **高度可配置** - 支持多种自定义选项
- ✅ **自动适应** - 智能检测和处理筛选元素
- ✅ **向后兼容** - 不影响现有功能
- ✅ **性能友好** - 最小化性能影响
