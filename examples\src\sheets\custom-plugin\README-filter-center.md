# 自定义筛选图标居中解决方案

## 📋 问题描述

默认情况下，Univer的筛选图标位于单元格的右下角，需要将其调整为垂直居中显示。

## 🎯 解决方案概览

我们提供了三种不同的解决方案来实现筛选图标的垂直居中：

### 1. **CenteredFilterPlugin** - 核心位置修补插件
通过修改筛选按钮的位置计算逻辑来实现居中效果。

### 2. **FilterIconStylePlugin** - 样式增强插件  
通过CSS样式和DOM观察来辅助实现居中效果。

### 3. **ConfigBasedFilterCenterPlugin** - 配置驱动插件
通过配置参数来控制筛选图标的位置和对齐方式。

## 🚀 使用方法

### 基础使用

```typescript
import { CenteredFilterPlugin, FilterIconStylePlugin, ConfigBasedFilterCenterPlugin } from './custom-plugin/centered-filter-plugin';

// 在Univer初始化时注册插件
univer.registerPlugins([
    // 其他插件...
    
    // 方案1: 基础居中插件
    [CenteredFilterPlugin],
    
    // 方案2: 样式增强插件
    [FilterIconStylePlugin],
    
    // 方案3: 配置驱动插件（推荐）
    [ConfigBasedFilterCenterPlugin, {
        iconSize: 16,           // 图标大小
        padding: 2,             // 边距
        verticalAlign: 'center' // 垂直对齐方式: 'top' | 'center' | 'bottom'
    }],
]);
```

### 高级配置

```typescript
// 自定义配置示例
[ConfigBasedFilterCenterPlugin, {
    iconSize: 18,              // 更大的图标
    padding: 3,                // 更大的边距
    verticalAlign: 'center'    // 居中对齐
}]

// 顶部对齐示例
[ConfigBasedFilterCenterPlugin, {
    iconSize: 14,
    padding: 1,
    verticalAlign: 'top'       // 顶部对齐
}]

// 底部对齐示例（默认行为）
[ConfigBasedFilterCenterPlugin, {
    iconSize: 16,
    padding: 1,
    verticalAlign: 'bottom'    // 底部对齐
}]
```

## 🔧 核心实现原理

### 位置计算公式

```typescript
// 原始位置计算（右下角）
const iconStartX = endX - FILTER_ICON_SIZE - FILTER_ICON_PADDING;
const iconStartY = endY - FILTER_ICON_SIZE - FILTER_ICON_PADDING;

// 居中位置计算（垂直居中）
const iconStartX = endX - FILTER_ICON_SIZE - FILTER_ICON_PADDING;
const iconStartY = startY + (cellHeight - FILTER_ICON_SIZE) / 2;
```

### 关键代码片段

```typescript
// 计算居中位置的工具函数
static calculateCenteredPosition(
    startX: number,
    startY: number,
    endX: number,
    endY: number,
    iconSize: number = 16,
    padding: number = 1
): { iconStartX: number; iconStartY: number } {
    const cellHeight = endY - startY;
    
    return {
        iconStartX: endX - iconSize - padding,        // 右对齐
        iconStartY: startY + (cellHeight - iconSize) / 2  // 垂直居中
    };
}
```

## 📝 自定义图标样式

如果需要自定义筛选图标的外观，可以参考 `custom-filter-icon.ts` 中的实现：

```typescript
// 自定义图标绘制
private drawCustomFilterIcon(ctx: UniverRenderingContext2D, size: number, fgColor: string, bgColor: string): void {
    ctx.save();

    // 绘制背景
    ctx.fillStyle = bgColor;
    ctx.fillRect(0, 0, size, size);

    // 绘制自定义图标形状
    ctx.strokeStyle = fgColor;
    ctx.lineWidth = 1.5;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // 绘制漏斗形状
    ctx.beginPath();
    ctx.moveTo(3, 4);
    ctx.lineTo(13, 4);
    ctx.moveTo(5, 7);
    ctx.lineTo(11, 7);
    ctx.moveTo(7, 10);
    ctx.lineTo(9, 10);
    ctx.lineTo(9, 13);
    ctx.stroke();

    ctx.restore();
}
```

## 🎨 CSS样式增强

`FilterIconStylePlugin` 还会添加以下CSS样式来增强效果：

```css
/* 自定义筛选图标样式 */
.filter-button-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 100%;
}

.filter-icon {
    vertical-align: middle;
}
```

## 🔍 调试和验证

### 验证插件是否生效

1. 打开浏览器开发者工具
2. 查看控制台输出，应该看到：
   ```
   ✅ Centered filter plugin: Successfully patched filter button positioning
   ✅ Filter icon styles added
   ✅ Filter configuration applied: {iconSize: 16, padding: 2, verticalAlign: "center"}
   ```

### 检查全局配置

在浏览器控制台中执行：
```javascript
// 检查配置是否正确设置
console.log(window.univerFilterConfig);

// 测试位置计算函数
console.log(window.calculateFilterIconPosition(0, 0, 100, 30));
```

## ⚠️ 注意事项

1. **插件加载顺序**：确保在筛选相关插件之后加载自定义插件
2. **性能影响**：这些插件对性能的影响很小，主要是一次性的位置计算修改
3. **兼容性**：插件设计为向后兼容，不会影响现有功能
4. **Canvas渲染**：由于Univer使用Canvas渲染，某些样式修改可能需要特殊处理

## 🔄 更新和维护

如果Univer更新导致插件失效，可能需要：

1. 检查筛选相关的类名和方法是否有变化
2. 更新位置计算逻辑
3. 调整CSS选择器

## 📞 技术支持

如果遇到问题，可以：

1. 检查浏览器控制台的错误信息
2. 确认插件是否正确注册
3. 验证Univer版本兼容性
4. 查看网络请求是否正常加载了相关资源
