<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Univer Sheets with Custom Filter Icons</title>
    
    <!-- 自定义筛选图标样式 -->
    <style>
        /* 筛选图标垂直居中样式 */
        :root {
            --filter-icon-size: 16px;
            --filter-icon-padding: 2px;
            --filter-vertical-align: center;
        }
        
        /* 全局筛选图标样式调整 */
        .univer-render-canvas,
        .univer-canvas-container {
            position: relative;
        }
        
        /* 通过CSS实现筛选图标垂直居中 */
        [class*="filter-button"],
        [class*="FilterButton"],
        .sheets-filter-button {
            transform: translateY(-50%) !important;
            top: 50% !important;
            position: relative !important;
        }
        
        /* 自定义筛选图标样式 */
        .custom-filter-icon {
            width: var(--filter-icon-size);
            height: var(--filter-icon-size);
            cursor: pointer;
            transition: all 0.2s ease;
            vertical-align: middle;
        }
        
        .custom-filter-icon:hover {
            opacity: 0.7;
            transform: scale(1.1);
        }
        
        .custom-filter-icon.has-criteria {
            color: #1976d2;
            fill: #1976d2;
        }
        
        /* Canvas相关样式 */
        canvas {
            /* 确保canvas正确处理筛选图标位置 */
        }
        
        /* 深色主题适配 */
        .univer-dark .custom-filter-icon {
            color: #ccc;
        }
        
        .univer-dark .custom-filter-icon:hover {
            color: #fff;
        }
        
        .univer-dark .custom-filter-icon.has-criteria {
            color: #64b5f6;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            :root {
                --filter-icon-size: 14px;
                --filter-icon-padding: 1px;
            }
        }
    </style>
</head>
<body>
    <div id="app" style="width: 100vw; height: 100vh;"></div>
    
    <!-- 筛选图标自定义脚本 -->
    <script>
        // 筛选图标位置自定义函数
        function customizeFilterIconPosition() {
            // 创建全局函数来计算居中位置
            window.calculateCenteredFilterPosition = function(startX, startY, endX, endY) {
                const cellWidth = endX - startX;
                const cellHeight = endY - startY;
                const iconSize = 16;
                const padding = 2;
                
                return {
                    iconStartX: endX - iconSize - padding,
                    iconStartY: startY + (cellHeight - iconSize) / 2, // 垂直居中
                    iconSize: iconSize
                };
            };
            
            // 监听DOM变化，处理动态添加的筛选图标
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                processFilterElements(node);
                            }
                        });
                    }
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            // 处理筛选元素
            function processFilterElements(element) {
                // 查找筛选相关元素
                const filterElements = element.querySelectorAll('[class*="filter"], [class*="Filter"], canvas');
                filterElements.forEach(function(el) {
                    if (!el.dataset.filterProcessed) {
                        el.dataset.filterProcessed = 'true';
                        el.classList.add('custom-filter-processed');
                    }
                });
            }
            
            // 处理已存在的元素
            setTimeout(function() {
                processFilterElements(document.body);
            }, 1000);
            
            console.log('✅ Filter icon position customizer initialized');
        }
        
        // 页面加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', customizeFilterIconPosition);
        } else {
            customizeFilterIconPosition();
        }
        
        // 提供外部配置接口
        window.customizeFilterIcon = function(options) {
            const defaults = {
                iconSize: 16,
                padding: 2,
                verticalAlign: 'center'
            };
            
            const config = Object.assign(defaults, options);
            
            // 更新CSS变量
            document.documentElement.style.setProperty('--filter-icon-size', config.iconSize + 'px');
            document.documentElement.style.setProperty('--filter-icon-padding', config.padding + 'px');
            
            console.log('Filter icon configuration updated:', config);
        };
    </script>
    
    <!-- 加载您的主要脚本 -->
    <script type="module">
        // 这里加载您的主要Univer初始化代码
        // 根据您的实际文件路径调整
        import('./main.js').then(module => {
            // 初始化Univer
            const { createUniver } = module;
            const { univerAPI, univer } = createUniver();
            
            // 将实例挂载到window对象
            window.univerAPI = univerAPI;
            window.univer = univer;
            
            console.log('✅ Univer initialized with custom filter icons');
            
            // 可选：自定义筛选图标配置
            setTimeout(() => {
                window.customizeFilterIcon({
                    iconSize: 16,
                    padding: 2,
                    verticalAlign: 'center'
                });
            }, 2000);
        }).catch(error => {
            console.error('Failed to load Univer:', error);
        });
    </script>
</body>
</html>
