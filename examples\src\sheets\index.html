<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Univer Sheets with Custom Filter Icons</title>

    <!-- 引入自定义样式文件 -->
    <link rel="stylesheet" href="./style.css">

    <!-- 筛选图标居中的内联样式（备用方案） -->
    <style>
        /* 确保筛选图标垂直居中的关键样式 */
        [class*="filter"]:not(.filter-center-processed) {
            transform: translateY(-50%) !important;
            top: 50% !important;
            position: relative !important;
        }

        /* 强制应用到Canvas容器 */
        .univer-canvas-container {
            position: relative;
        }

        /* 图标基础样式 */
        .filter-icon-centered {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-icon-centered:hover {
            opacity: 0.8;
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div id="app" style="width: 100vw; height: 100vh;"></div>

    <!-- 引入外部筛选图标居中脚本 -->
    <script src="./filter-center-external.js"></script>

    <!-- 简化的筛选图标自定义脚本 -->
    <script>
        // 页面加载完成后的配置
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 Page loaded, configuring filter icons...');

            // 配置筛选图标居中
            if (window.configureFilterCenter) {
                window.configureFilterCenter({
                    iconSize: 16,
                    padding: 2,
                    verticalAlign: 'center',
                    debug: true
                });
            }

            // 延迟处理，确保Univer完全加载
            setTimeout(function() {
                if (window.processFilterElements) {
                    window.processFilterElements();
                }
            }, 3000);
        });

        // 提供简化的配置接口
        window.setFilterAlignment = function(alignment) {
            if (window.configureFilterCenter) {
                window.configureFilterCenter({
                    verticalAlign: alignment // 'top', 'center', 'bottom'
                });
                console.log('Filter alignment set to:', alignment);
            }
        };

        // 提供图标大小调整接口
        window.setFilterIconSize = function(size) {
            if (window.configureFilterCenter) {
                window.configureFilterCenter({
                    iconSize: size
                });
                console.log('Filter icon size set to:', size);
            }
        };
    </script>

    <!-- 加载您的主要脚本 -->
    <script type="module">
        // 这里加载您的主要Univer初始化代码
        // 根据您的实际文件路径调整
        import('./main.js').then(module => {
            // 初始化Univer
            const { createUniver } = module;
            const { univerAPI, univer } = createUniver();

            // 将实例挂载到window对象
            window.univerAPI = univerAPI;
            window.univer = univer;

            console.log('✅ Univer initialized with custom filter icons');

            // 可选：自定义筛选图标配置
            setTimeout(() => {
                window.customizeFilterIcon({
                    iconSize: 16,
                    padding: 2,
                    verticalAlign: 'center'
                });
            }, 2000);
        }).catch(error => {
            console.error('Failed to load Univer:', error);
        });
    </script>
</body>
</html>
