/**
 * 外部筛选图标居中脚本 - 不修改源码方案
 * 通过DOM操作和样式注入实现筛选图标垂直居中
 */

(function() {
    'use strict';
    
    // 配置选项
    const CONFIG = {
        iconSize: 16,
        padding: 2,
        verticalAlign: 'center', // 'top', 'center', 'bottom'
        checkInterval: 1000, // 检查间隔（毫秒）
        debug: true
    };

    // 日志函数
    function log(message, ...args) {
        if (CONFIG.debug) {
            console.log('[FilterCenter]', message, ...args);
        }
    }

    // 注入CSS样式
    function injectStyles() {
        const styleId = 'external-filter-center-styles';
        
        if (document.getElementById(styleId)) {
            return;
        }

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            /* 外部筛选图标居中样式 */
            .filter-center-processed {
                ${getVerticalAlignCSS()}
            }
            
            /* 强制应用到所有可能的筛选元素 */
            [class*="filter"]:not(.filter-center-processed),
            [class*="Filter"]:not(.filter-center-processed) {
                ${getVerticalAlignCSS()}
            }
            
            /* Canvas相关调整 */
            .univer-canvas-container {
                position: relative;
            }
            
            /* 图标悬停效果 */
            .filter-center-processed:hover {
                opacity: 0.8;
                transform: ${getHoverTransform()};
            }
            
            /* 有筛选条件的样式 */
            .filter-center-processed.has-criteria {
                color: #1976d2 !important;
                fill: #1976d2 !important;
            }
        `;
        
        document.head.appendChild(style);
        log('Styles injected');
    }

    // 获取垂直对齐CSS
    function getVerticalAlignCSS() {
        switch (CONFIG.verticalAlign) {
            case 'top':
                return `
                    transform: translateY(${CONFIG.padding}px) !important;
                    top: ${CONFIG.padding}px !important;
                    position: relative !important;
                `;
            case 'bottom':
                return `
                    transform: translateY(-${CONFIG.padding}px) !important;
                    bottom: ${CONFIG.padding}px !important;
                    position: relative !important;
                `;
            case 'center':
            default:
                return `
                    transform: translateY(-50%) !important;
                    top: 50% !important;
                    position: relative !important;
                `;
        }
    }

    // 获取悬停变换效果
    function getHoverTransform() {
        const baseTransform = CONFIG.verticalAlign === 'center' ? 'translateY(-50%)' : 'translateY(0)';
        return `${baseTransform} scale(1.05) !important`;
    }

    // 查找筛选相关元素
    function findFilterElements(container = document) {
        const selectors = [
            // 类名选择器
            '[class*="filter"]',
            '[class*="Filter"]',
            '[class*="FILTER"]',
            
            // 属性选择器
            '[data-filter]',
            '[data-filter-button]',
            '[aria-label*="filter" i]',
            '[aria-label*="筛选" i]',
            '[title*="filter" i]',
            '[title*="筛选" i]',
            
            // 可能的ID选择器
            '[id*="filter"]',
            '[id*="Filter"]'
        ];

        const elements = [];
        selectors.forEach(selector => {
            try {
                const found = container.querySelectorAll(selector);
                elements.push(...Array.from(found));
            } catch (e) {
                // 忽略无效选择器
            }
        });

        // 去重
        return [...new Set(elements)];
    }

    // 处理筛选元素
    function processFilterElements(elements) {
        let processedCount = 0;
        
        elements.forEach(element => {
            if (element.classList.contains('filter-center-processed')) {
                return; // 已处理过
            }

            // 检查是否真的是筛选相关元素
            if (isFilterElement(element)) {
                element.classList.add('filter-center-processed');
                
                // 检查是否有筛选条件
                if (hasFilterCriteria(element)) {
                    element.classList.add('has-criteria');
                }
                
                processedCount++;
                log('Processed filter element:', element);
            }
        });

        if (processedCount > 0) {
            log(`Processed ${processedCount} filter elements`);
        }
    }

    // 判断是否为筛选元素
    function isFilterElement(element) {
        const className = element.className.toString().toLowerCase();
        const tagName = element.tagName.toLowerCase();
        
        // 排除明显不是筛选图标的元素
        if (tagName === 'script' || tagName === 'style' || tagName === 'link') {
            return false;
        }
        
        // 检查尺寸，筛选图标通常比较小
        const rect = element.getBoundingClientRect();
        if (rect.width > 100 || rect.height > 100) {
            return false;
        }
        
        // 检查是否包含筛选相关关键词
        const filterKeywords = ['filter', 'sort', 'dropdown', 'arrow'];
        return filterKeywords.some(keyword => className.includes(keyword));
    }

    // 检查是否有筛选条件
    function hasFilterCriteria(element) {
        // 检查元素的属性和类名
        const className = element.className.toString().toLowerCase();
        const hasActiveClass = className.includes('active') || 
                              className.includes('selected') || 
                              className.includes('criteria');
        
        // 检查aria属性
        const ariaPressed = element.getAttribute('aria-pressed');
        const ariaSelected = element.getAttribute('aria-selected');
        
        return hasActiveClass || ariaPressed === 'true' || ariaSelected === 'true';
    }

    // 观察DOM变化
    function observeChanges() {
        const observer = new MutationObserver((mutations) => {
            let hasRelevantChanges = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const filterElements = findFilterElements(node);
                            if (filterElements.length > 0) {
                                hasRelevantChanges = true;
                                processFilterElements(filterElements);
                            }
                        }
                    });
                } else if (mutation.type === 'attributes') {
                    const target = mutation.target;
                    if (isFilterElement(target)) {
                        hasRelevantChanges = true;
                        processFilterElements([target]);
                    }
                }
            });
            
            if (hasRelevantChanges) {
                log('DOM changes detected, processed filter elements');
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class', 'aria-pressed', 'aria-selected']
        });

        log('DOM observer started');
    }

    // 定期检查和处理
    function startPeriodicCheck() {
        setInterval(() => {
            const filterElements = findFilterElements();
            const unprocessed = filterElements.filter(el => 
                !el.classList.contains('filter-center-processed')
            );
            
            if (unprocessed.length > 0) {
                processFilterElements(unprocessed);
            }
        }, CONFIG.checkInterval);
    }

    // 初始化
    function init() {
        log('Initializing external filter center...');
        
        // 注入样式
        injectStyles();
        
        // 处理现有元素
        setTimeout(() => {
            const filterElements = findFilterElements();
            processFilterElements(filterElements);
        }, 500);
        
        // 观察变化
        observeChanges();
        
        // 定期检查
        startPeriodicCheck();
        
        log('External filter center initialized');
    }

    // 提供外部配置接口
    window.configureFilterCenter = function(options) {
        Object.assign(CONFIG, options);
        
        // 重新注入样式
        const oldStyle = document.getElementById('external-filter-center-styles');
        if (oldStyle) {
            oldStyle.remove();
        }
        injectStyles();
        
        log('Configuration updated:', CONFIG);
    };

    // 提供手动处理接口
    window.processFilterElements = function() {
        const filterElements = findFilterElements();
        processFilterElements(filterElements);
        log('Manual processing completed');
    };

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 确保在Univer加载后也能工作
    setTimeout(init, 2000);

})();
