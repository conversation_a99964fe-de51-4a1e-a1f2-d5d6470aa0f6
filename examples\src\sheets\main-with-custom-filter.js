import { UniverSheetsCorePreset } from "@univerjs/preset-sheets-core";
import UniverPresetSheetsCoreZhCN from "@univerjs/preset-sheets-core/locales/zh-CN";

import { UniverSheetsSortPreset } from "@univerjs/preset-sheets-sort";
import UniverPresetSheetsSortZhCN from "@univerjs/preset-sheets-sort/locales/zh-CN";

import { UniverSheetsFilterPreset } from "@univerjs/preset-sheets-filter";
import UniverPresetSheetsFilterZhCN from "@univerjs/preset-sheets-filter/locales/zh-CN";

import { UniverSheetsFindReplacePreset } from "@univerjs/preset-sheets-find-replace";
import UniverPresetSheetsFindReplaceZhCN from "@univerjs/preset-sheets-find-replace/locales/zh-CN";

import { UniverSheetsNotePreset } from "@univerjs/preset-sheets-note";
import UniverPresetSheetsNoteZhCN from "@univerjs/preset-sheets-note/locales/zh-CN";

import { UniverSheetsDataValidationPreset } from "@univerjs/preset-sheets-data-validation";
import UniverPresetSheetsDataValidationZhCN from "@univerjs/preset-sheets-data-validation/locales/zh-CN";
import { UNIVER_SHEET_PERMISSION_BACKGROUND } from "@univerjs/preset-sheets-core";
import { createUniver, LocaleType, merge } from "@univerjs/presets";
import "@univerjs/preset-sheets-core/lib/index.css";
import "@univerjs/preset-sheets-filter/lib/index.css";
import "@univerjs/preset-sheets-find-replace/lib/index.css";
import "@univerjs/preset-sheets-data-validation/lib/index.css";
import "@univerjs/preset-sheets-sort/lib/index.css";
import "@univerjs/preset-sheets-note/lib/index.css";

/**
 * 筛选图标自定义器 - 集成版本
 */
class IntegratedFilterCustomizer {
  constructor() {
    this.config = {
      iconSize: 16,
      padding: 2,
      verticalAlign: 'center'
    };
    this.init();
  }

  init() {
    this.injectStyles();
    this.setupGlobalFunctions();
    this.observeChanges();
    console.log('✅ Integrated Filter Customizer initialized');
  }

  injectStyles() {
    const style = document.createElement('style');
    style.id = 'integrated-filter-customizer';
    style.textContent = `
      /* 筛选图标垂直居中样式 */
      :root {
        --filter-icon-size: ${this.config.iconSize}px;
        --filter-icon-padding: ${this.config.padding}px;
      }
      
      /* 全局筛选图标样式调整 */
      .univer-render-canvas,
      .univer-canvas-container {
        position: relative;
      }
      
      /* 筛选图标垂直居中 */
      [class*="filter-button"],
      [class*="FilterButton"],
      .sheets-filter-button {
        ${this.getVerticalAlignCSS()}
      }
      
      /* 自定义筛选图标样式 */
      .custom-filter-icon {
        width: var(--filter-icon-size);
        height: var(--filter-icon-size);
        cursor: pointer;
        transition: all 0.2s ease;
        ${this.getVerticalAlignCSS()}
      }
      
      .custom-filter-icon:hover {
        opacity: 0.7;
        transform: scale(1.1);
      }
      
      .custom-filter-icon.has-criteria {
        color: #1976d2;
        fill: #1976d2;
      }
      
      /* 深色主题适配 */
      .univer-dark .custom-filter-icon {
        color: #ccc;
      }
      
      .univer-dark .custom-filter-icon:hover {
        color: #fff;
      }
      
      .univer-dark .custom-filter-icon.has-criteria {
        color: #64b5f6;
      }
    `;
    document.head.appendChild(style);
  }

  getVerticalAlignCSS() {
    switch (this.config.verticalAlign) {
      case 'top':
        return `
          transform: translateY(${this.config.padding}px) !important;
          top: ${this.config.padding}px !important;
        `;
      case 'bottom':
        return `
          transform: translateY(-${this.config.padding}px) !important;
          bottom: ${this.config.padding}px !important;
        `;
      case 'center':
      default:
        return `
          transform: translateY(-50%) !important;
          top: 50% !important;
          position: relative !important;
        `;
    }
  }

  setupGlobalFunctions() {
    // 全局位置计算函数
    window.calculateCenteredFilterPosition = (startX, startY, endX, endY) => {
      const cellWidth = endX - startX;
      const cellHeight = endY - startY;
      
      let iconStartY;
      switch (this.config.verticalAlign) {
        case 'top':
          iconStartY = startY + this.config.padding;
          break;
        case 'bottom':
          iconStartY = endY - this.config.iconSize - this.config.padding;
          break;
        case 'center':
        default:
          iconStartY = startY + (cellHeight - this.config.iconSize) / 2;
          break;
      }
      
      return {
        iconStartX: endX - this.config.iconSize - this.config.padding,
        iconStartY: iconStartY,
        iconSize: this.config.iconSize
      };
    };

    // 配置更新函数
    window.customizeFilterIcon = (options) => {
      Object.assign(this.config, options);
      this.updateStyles();
      console.log('Filter icon configuration updated:', this.config);
    };
  }

  updateStyles() {
    const existingStyle = document.getElementById('integrated-filter-customizer');
    if (existingStyle) {
      existingStyle.remove();
    }
    this.injectStyles();
  }

  observeChanges() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.processFilterElements(node);
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // 处理已存在的元素
    setTimeout(() => {
      this.processFilterElements(document.body);
    }, 1000);
  }

  processFilterElements(element) {
    const filterElements = element.querySelectorAll('[class*="filter"], [class*="Filter"], canvas');
    filterElements.forEach((el) => {
      if (!el.dataset.filterProcessed) {
        el.dataset.filterProcessed = 'true';
        el.classList.add('custom-filter-processed');
      }
    });
  }
}

// 创建 Univer 实例的函数
function createUniverInstance() {
  // 初始化筛选图标自定义器
  const filterCustomizer = new IntegratedFilterCustomizer();

  const { univerAPI, univer } = createUniver({
    locale: LocaleType.ZH_CN,
    locales: {
      [LocaleType.ZH_CN]: merge(
        {},
        UniverPresetSheetsCoreZhCN,
        UniverPresetSheetsFilterZhCN,
        UniverPresetSheetsFindReplaceZhCN,
        UniverPresetSheetsDataValidationZhCN,
        UniverPresetSheetsSortZhCN,
        UniverPresetSheetsNoteZhCN
      ),
    },
    presets: [
      UniverSheetsCorePreset({
        // 禁用头部工具栏
        header: false,
        toolbar: false,
        // 禁用底部所有组件
        footer: false,
        // 保留右键菜单但限制功能
        contextMenu: true,
        // 自定义菜单配置，只保留基本的复制粘贴插入删除功能
        menu: {
          "sheet.contextMenu.permission": { hidden: true },
          "sheet.menu.sheet-frozen": { hidden: true },
          "sheet.menu.paste-special": { hidden: true },
          "sheet.menu.clear-selection": { hidden: true },
          "sheet.command.set-row-height": { hidden: true },
          "sheet.command.set-worksheet-col-width": { hidden: true },
          "sheet.command.insert-multi-cols-before": { hidden: true },
          "sheet.command.insert-multi-cols-right": { hidden: true },
          "sheet.command.hide-col-confirm": { hidden: true },
          "sheet.command.set-col-auto-width": { hidden: true },
          "sheet.menu.delete": { hidden: true },
          "sheet.menu.cell-insert": { hidden: true },
          "sheet.command.insert-multi-rows-above": { hidden: true },
          "sheet.command.insert-multi-rows-after": { hidden: true },
          "sheet.command.hide-row-confirm": { hidden: true },
          "sheet.command.set-row-is-auto-height": { hidden: true },
          "sheet.command.copy": { hidden: false },
          "sheet.command.paste": { hidden: false },
          "sheet.command.cut": { hidden: true },
          "sheet.command.undo": { hidden: true },
          "sheet.command.redo": { hidden: true },
          "sheet.operation.add-note-popup": { hidden: true },
          "sheet.command.delete-note": { hidden: true },
          "sheet.command.toggle-note-popup": { hidden: true },
          "sheet.menu.sheets-sort-ctx-popup": { hidden: true },
          "sheet.menu.sheets-sort": { hidden: true },
        },
        customComponents: new Set([UNIVER_SHEET_PERMISSION_BACKGROUND]),
      }),
      UniverSheetsFilterPreset(),
      UniverSheetsFindReplacePreset(),
      UniverSheetsDataValidationPreset(),
      UniverSheetsSortPreset(),
      UniverSheetsNotePreset(),
    ],
  });

  // 延迟应用筛选图标自定义
  setTimeout(() => {
    window.customizeFilterIcon({
      iconSize: 16,
      padding: 2,
      verticalAlign: 'center'
    });
  }, 2000);

  return { univerAPI, univer };
}

// 将函数挂载到 window 对象，保持向后兼容
window.createUniver = createUniverInstance;

// 导出给 IIFE 格式使用
export { createUniverInstance as createUniver };
