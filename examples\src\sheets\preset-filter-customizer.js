/**
 * Preset模式下的筛选图标自定义器
 * 专门针对使用@univerjs/preset-*包的场景
 */

class PresetFilterCustomizer {
  constructor() {
    this.initialized = false;
    this.config = {
      iconSize: 16,
      padding: 2,
      verticalAlign: 'center', // 'top', 'center', 'bottom'
      hoverEffect: true,
      customColors: {
        normal: '#666',
        hover: '#333',
        active: '#1976d2'
      }
    };
    
    this.init();
  }

  init() {
    // 等待DOM和Univer完全加载
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.delayedInit());
    } else {
      this.delayedInit();
    }
  }

  delayedInit() {
    // 延迟初始化，确保Univer已经完全加载
    setTimeout(() => {
      this.setup();
    }, 1000);
  }

  setup() {
    if (this.initialized) return;
    
    this.injectCustomStyles();
    this.patchFilterPositioning();
    this.observeFilterChanges();
    this.initialized = true;
    
    console.log('✅ Preset Filter Customizer initialized');
  }

  /**
   * 注入自定义样式来调整筛选图标位置
   */
  injectCustomStyles() {
    const styleId = 'preset-filter-customizer';
    
    if (document.getElementById(styleId)) {
      return;
    }

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      /* Preset模式筛选图标自定义样式 */
      
      /* 全局CSS变量 */
      :root {
        --filter-icon-size: ${this.config.iconSize}px;
        --filter-icon-padding: ${this.config.padding}px;
        --filter-icon-color: ${this.config.customColors.normal};
        --filter-icon-hover-color: ${this.config.customColors.hover};
        --filter-icon-active-color: ${this.config.customColors.active};
      }
      
      /* 筛选图标容器调整 */
      .univer-render-canvas,
      .univer-canvas-container {
        position: relative;
      }
      
      /* 通过CSS变换实现垂直居中 */
      .univer-render-canvas::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 999;
      }
      
      /* 如果存在特定的筛选相关类名 */
      [class*="filter"],
      [class*="Filter"] {
        ${this.getVerticalAlignCSS()}
      }
      
      /* Canvas内部元素的位置调整 */
      .univer-canvas-container canvas {
        /* 确保canvas正确处理筛选图标 */
      }
      
      /* 自定义筛选图标样式 */
      .custom-filter-icon {
        width: var(--filter-icon-size);
        height: var(--filter-icon-size);
        color: var(--filter-icon-color);
        cursor: pointer;
        transition: all 0.2s ease;
        ${this.getVerticalAlignCSS()}
      }
      
      .custom-filter-icon:hover {
        color: var(--filter-icon-hover-color);
        transform: scale(1.1);
      }
      
      .custom-filter-icon.has-criteria {
        color: var(--filter-icon-active-color);
      }
      
      /* 深色主题适配 */
      .univer-dark .custom-filter-icon {
        --filter-icon-color: #ccc;
        --filter-icon-hover-color: #fff;
        --filter-icon-active-color: #64b5f6;
      }
      
      /* 响应式调整 */
      @media (max-width: 768px) {
        :root {
          --filter-icon-size: 14px;
          --filter-icon-padding: 1px;
        }
      }
    `;
    
    document.head.appendChild(style);
  }

  /**
   * 获取垂直对齐的CSS
   */
  getVerticalAlignCSS() {
    switch (this.config.verticalAlign) {
      case 'top':
        return `
          vertical-align: top;
          transform: translateY(${this.config.padding}px);
        `;
      case 'bottom':
        return `
          vertical-align: bottom;
          transform: translateY(-${this.config.padding}px);
        `;
      case 'center':
      default:
        return `
          vertical-align: middle;
          transform: translateY(-50%);
          position: relative;
          top: 50%;
        `;
    }
  }

  /**
   * 修补筛选位置计算
   */
  patchFilterPositioning() {
    // 创建全局函数供Univer内部使用
    window.calculateCenteredFilterPosition = (startX, startY, endX, endY) => {
      const cellWidth = endX - startX;
      const cellHeight = endY - startY;
      
      let iconStartY;
      switch (this.config.verticalAlign) {
        case 'top':
          iconStartY = startY + this.config.padding;
          break;
        case 'bottom':
          iconStartY = endY - this.config.iconSize - this.config.padding;
          break;
        case 'center':
        default:
          iconStartY = startY + (cellHeight - this.config.iconSize) / 2;
          break;
      }
      
      return {
        iconStartX: endX - this.config.iconSize - this.config.padding,
        iconStartY: iconStartY,
        iconSize: this.config.iconSize
      };
    };

    // 尝试拦截和修改筛选相关的渲染逻辑
    this.interceptFilterRendering();
  }

  /**
   * 拦截筛选渲染
   */
  interceptFilterRendering() {
    // 监听可能的筛选相关事件
    document.addEventListener('click', (e) => {
      // 检查是否点击了筛选相关元素
      if (this.isFilterRelatedElement(e.target)) {
        this.handleFilterInteraction(e);
      }
    });

    // 使用MutationObserver监听DOM变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' || mutation.type === 'childList') {
          this.checkForFilterElements(mutation.target);
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style']
    });
  }

  /**
   * 检查是否为筛选相关元素
   */
  isFilterRelatedElement(element) {
    if (!element || !element.className) return false;
    
    const className = element.className.toString().toLowerCase();
    return className.includes('filter') || 
           className.includes('sort') ||
           element.tagName === 'CANVAS';
  }

  /**
   * 处理筛选交互
   */
  handleFilterInteraction(event) {
    console.log('Filter interaction detected:', event.target);
    
    // 在这里可以添加自定义的筛选逻辑
    // 例如显示自定义的筛选面板等
  }

  /**
   * 检查筛选元素
   */
  checkForFilterElements(element) {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) return;
    
    // 检查是否有筛选相关的元素需要处理
    if (this.isFilterRelatedElement(element)) {
      this.processFilterElement(element);
    }
  }

  /**
   * 处理筛选元素
   */
  processFilterElement(element) {
    if (element.dataset.filterProcessed) return;
    
    element.dataset.filterProcessed = 'true';
    
    // 添加自定义类名
    element.classList.add('custom-filter-processed');
    
    console.log('Filter element processed:', element);
  }

  /**
   * 观察筛选变化
   */
  observeFilterChanges() {
    // 定期检查并更新筛选图标
    setInterval(() => {
      this.updateFilterIcons();
    }, 2000);
  }

  /**
   * 更新筛选图标
   */
  updateFilterIcons() {
    const canvases = document.querySelectorAll('canvas');
    canvases.forEach(canvas => {
      if (!canvas.dataset.filterObserved) {
        canvas.dataset.filterObserved = 'true';
        this.observeCanvas(canvas);
      }
    });
  }

  /**
   * 观察Canvas
   */
  observeCanvas(canvas) {
    // 监听canvas的变化
    if (window.ResizeObserver) {
      const resizeObserver = new ResizeObserver(() => {
        this.handleCanvasResize(canvas);
      });
      resizeObserver.observe(canvas);
    }
  }

  /**
   * 处理Canvas大小变化
   */
  handleCanvasResize(canvas) {
    console.log('Canvas resized, updating filter positions:', canvas);
    // 这里可以添加重新计算筛选图标位置的逻辑
  }

  /**
   * 公共API：更新配置
   */
  updateConfig(newConfig) {
    Object.assign(this.config, newConfig);
    
    // 重新注入样式
    const oldStyle = document.getElementById('preset-filter-customizer');
    if (oldStyle) {
      oldStyle.remove();
    }
    this.injectCustomStyles();
    
    console.log('Filter customizer config updated:', this.config);
  }

  /**
   * 公共API：获取当前配置
   */
  getConfig() {
    return { ...this.config };
  }
}

// 自动初始化
const presetFilterCustomizer = new PresetFilterCustomizer();

// 挂载到window对象供外部使用
window.PresetFilterCustomizer = PresetFilterCustomizer;
window.presetFilterCustomizer = presetFilterCustomizer;

// 提供便捷的配置函数
window.customizeFilterIcon = (config) => {
  presetFilterCustomizer.updateConfig(config);
};

console.log('🎯 Preset Filter Customizer loaded');

export default PresetFilterCustomizer;
